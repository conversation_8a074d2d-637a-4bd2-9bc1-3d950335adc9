<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推薦函已提交通知</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: #d4edda;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            text-align: center;
        }

        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info-box {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }

        .success-icon {
            color: #28a745;
            font-size: 24px;
        }

        .footer {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>推薦人已提交推薦函 Recommender Has Submitted Recommendation Letter</h1>
        <p>您的推薦函已完成 Your recommendation letter has been completed</p>
    </div>

    <div class="content">
        <p>親愛的 {{ $applicant->user->name }} 同學，您好：</p>
        <p>Dear {{ $applicant->user->name }},</p>

        <p>我們很高興通知您，以下推薦函已經成功提交：</p>
        <p>We are pleased to inform you that the following recommendation letter has been successfully submitted:</p>

        <div class="info-box">
            <h3>推薦函資訊 Recommendation Letter Information</h3>
            <ul>
                <li><strong>推薦人 Recommender：</strong>{{ $recommender->name }} {{ $recommender->title ?? '' }}</li>
                <li><strong>推薦人單位 Recommender's Institution：</strong>{{ $recommender->department ?? '未提供 Not provided' }}</li>
                <li><strong>招生類別 Program Type：</strong>{{ $recommendationLetter->program_type ?? '未提供 Not provided' }}</li>
                <li><strong>招生科系 Department：</strong>{{ $recommendationLetter->department_name ?? '未提供 Not provided' }}</li>
                <li><strong>申請表編號 Application Form ID：</strong>{{ $recommendationLetter->application_form_id ?? '未提供 Not provided' }}</li>
                <li><strong>提交時間 Submission Time：</strong>{{ $submitted_at->format('Y年m月d日 H:i') }} {{ $submitted_at->format('F j, Y H:i') }}</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>下一步行動 Next Steps</h3>
            <ul>
                <li>您可以登入系統查看推薦函狀態 You can log into the system to check the status of recommendation letters</li>
                <li>如需要更多推薦函，請繼續邀請其他推薦人 If you need more recommendation letters, please continue to invite other recommenders</li>
                <li>請確認所有必要的推薦函都已收集完成 Please confirm that all necessary recommendation letters have been collected</li>
                <li>注意申請截止日期，及時完成所有申請程序 Pay attention to application deadlines and complete all application procedures in time</li>
            </ul>
        </div>

        <p>感謝您使用推薦函管理系統。祝您申請順利！</p>
        <p>Thank you for using the recommendation letter management system. We wish you success with your application!</p>
    </div>

    <div class="footer">
        <p>此郵件由推薦函管理系統自動發送</p>
        <p>This email is automatically sent by the recommendation letter management system</p>
        <p>如有任何問題，請聯繫系統管理員</p>
        <p>If you have any questions, please contact the system administrator</p>
        <p>發送時間 Sent at：{{ now()->format('Y-m-d H:i:s') }}</p>
    </div>
</body>

</html>