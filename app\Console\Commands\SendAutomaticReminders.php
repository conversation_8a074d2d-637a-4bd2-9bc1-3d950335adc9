<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 發送自動提醒信的 Artisan 命令
 */
class SendAutomaticReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recommendations:send-reminders 
                            {--dry-run : 僅顯示將要發送的提醒，不實際發送}
                            {--force : 強制發送，忽略冷卻時間限制}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '發送自動提醒信給尚未回應的推薦人';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('開始執行自動提醒信發送任務...');

        try {
            $emailService = new EmailService();
            
            if ($this->option('dry-run')) {
                $this->info('執行模擬模式，不會實際發送郵件');
                // 這裡可以加入模擬邏輯
                $this->info('模擬完成');
                return Command::SUCCESS;
            }

            $sentCount = $emailService->sendAutomaticReminders();

            $this->info("自動提醒信發送完成，共發送 {$sentCount} 封郵件");

            Log::info('自動提醒信命令執行完成', [
                'sent_count' => $sentCount,
                'executed_at' => now(),
                'command' => $this->signature,
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('自動提醒信發送失敗: ' . $e->getMessage());

            Log::error('自動提醒信命令執行失敗', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'executed_at' => now(),
            ]);

            return Command::FAILURE;
        }
    }
}
