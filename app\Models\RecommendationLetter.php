<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

/**
 * 推薦函模型
 *
 * 管理推薦函的完整生命週期，包括邀請、接受、提交等狀態
 */
class RecommendationLetter extends Model
{
  use HasFactory;

  /**
   * 推薦函狀態常數
   */
  const STATUS_PENDING = 'pending';     // 推薦人尚未回應
  const STATUS_DECLINED = 'declined';   // 推薦人婉拒
  const STATUS_SUBMITTED = 'submitted'; // 推薦人已提交推薦函
  const STATUS_WITHDRAWN = 'withdrawn'; // 申請人撤回
  const STATUS_TIMEOUT = 'timeout';     // 超時未處理

  /**
   * 提交方式常數
   */
  const SUBMISSION_TYPE_PDF = 'pdf';
  const SUBMISSION_TYPE_QUESTIONNAIRE = 'questionnaire';

  /**
   * 可批量賦值的屬性
   *
   * @var array<string>
   */
  protected $fillable = [
    'applicant_id',           // 申請人 ID
    'recommender_id',         // 推薦人 ID
    'application_form_id',    // 申請表編號
    'recommender_email',      // 推薦人電子郵件
    'recommender_name',       // 推薦人姓名
    'recommender_title',      // 推薦人職稱
    'recommender_department', // 推薦人部門
    'recommender_phone',      // 推薦人電話
    'department_name',        // 系所名稱
    'program_type',          // 學程類型
    'status',                // 狀態
    'pdf_path',             // PDF 檔案路徑
    'questionnaire_data',   // 問卷資料
    'submission_type',      // 提交方式
    'submitted_at',         // 提交時間
    'last_reminded_at',     // 最後提醒時間
  ];

  /**
   * 屬性類型轉換
   *
   * @var array<string, string>
   */
  protected $casts = [
    'submitted_at' => 'datetime',
    'last_reminded_at' => 'datetime',
    'questionnaire_data' => 'array',
  ];

  /**
   * 取得推薦函關聯的申請人
   *
   * @return BelongsTo
   */
  public function applicant(): BelongsTo
  {
    return $this->belongsTo(Applicant::class);
  }

  /**
   * 取得推薦函關聯的推薦人
   *
   * @return BelongsTo
   */
  public function recommender(): BelongsTo
  {
    return $this->belongsTo(Recommender::class);
  }

  /**
   * 取得推薦函的郵件記錄
   *
   * @return HasMany
   */
  public function emailLogs(): HasMany
  {
    return $this->hasMany(EmailLog::class);
  }

  /**
   * 檢查推薦函是否為待處理狀態
   *
   * @return bool
   */
  public function isPending(): bool
  {
    return $this->status === self::STATUS_PENDING;
  }

  /**
   * 檢查推薦函是否已被拒絕
   *
   * @return bool
   */
  public function isDeclined(): bool
  {
    return $this->status === self::STATUS_DECLINED;
  }

  /**
   * 檢查推薦函是否已提交
   *
   * @return bool
   */
  public function isSubmitted(): bool
  {
    return $this->status === self::STATUS_SUBMITTED;
  }

  /**
   * 檢查推薦函是否已撤回
   *
   * @return bool
   */
  public function isWithdrawn(): bool
  {
    return $this->status === self::STATUS_WITHDRAWN;
  }

  /**
   * 檢查是否可以發送提醒
   *
   * @param int $cooldownHours 冷卻時間（小時）
   * @return bool
   */
  public function canSendReminder(int $cooldownHours = 24): bool
  {
    // 只有待處理或超時狀態可以發送提醒
    if (!in_array($this->status, [self::STATUS_PENDING, self::STATUS_TIMEOUT])) {
      return false;
    }

    if (!$this->last_reminded_at) {
      return true;
    }

    return $this->last_reminded_at->addHours($cooldownHours)->isPast();
  }

  /**
   * 更新最後提醒時間
   *
   * @return void
   */
  public function updateLastReminded(): void
  {
    $this->update(['last_reminded_at' => now()]);
  }

  /**
   * 拒絕推薦函邀請
   *
   * @return bool
   */
  public function decline(): bool
  {
    if (!$this->isPending()) {
      return false;
    }

    $updateData = ['status' => self::STATUS_DECLINED];

    $updated = $this->update($updateData);

    if ($updated) {
      // 發送婉拒通知信件給申請人
      $this->sendDeclineNotification();
    }

    return $updated;
  }

  /**
   * 提交推薦函
   *
   * @param string $submissionType 提交方式
   * @param array|null $data 額外資料
   * @return bool
   */
  public function submit(string $submissionType, ?array $data = null): bool
  {
    if (!$this->isPending()) {
      return false;
    }

    $updateData = [
      'status' => self::STATUS_SUBMITTED,
      'submission_type' => $submissionType,
      'submitted_at' => now(),
    ];

    if ($data) {
      if ($submissionType === self::SUBMISSION_TYPE_PDF && isset($data['pdf_path'])) {
        $updateData['pdf_path'] = $data['pdf_path'];
      } elseif ($submissionType === self::SUBMISSION_TYPE_QUESTIONNAIRE) {
        if (isset($data['questionnaire_data'])) {
          $updateData['questionnaire_data'] = $data['questionnaire_data'];
        }
        if (isset($data['pdf_path'])) {
          $updateData['pdf_path'] = $data['pdf_path'];
        }
      }
    }

    return $this->update($updateData);
  }

  /**
   * 撤回推薦函
   *
   * @return bool
   */
  public function withdraw(): bool
  {
    if ($this->isSubmitted()) {
      return false; // 已提交的推薦函不能撤回
    }

    return $this->update(['status' => self::STATUS_WITHDRAWN]);
  }

  /**
   * 取得 PDF 檔案的完整 URL
   *
   * @return string|null
   */
  public function getPdfUrlAttribute(): ?string
  {
    return $this->pdf_path ? Storage::url($this->pdf_path) : null;
  }

  /**
   * 取得推薦函的顯示標題
   *
   * @return string
   */
  public function getDisplayTitleAttribute(): string
  {
    return "{$this->program_type}（{$this->department_name}）";
  }

  /**
   * 取得狀態的中文顯示
   *
   * @return string
   */
  public function getStatusDisplayAttribute(): string
  {
    return match ($this->status) {
      self::STATUS_PENDING => '等待推薦人回應',
      self::STATUS_DECLINED => '推薦人不克擔任',
      self::STATUS_SUBMITTED => '推薦人已上傳完成',
      self::STATUS_WITHDRAWN => '考生已撤回申請',
      self::STATUS_TIMEOUT => '超時未處理',
      default => '未知狀態',
    };
  }

  /**
   * 檢查推薦函是否已超時
   *
   * @return bool
   */
  public function isTimeout(): bool
  {
    return $this->status === self::STATUS_TIMEOUT;
  }



  /**
   * 檢查推薦函是否可以重新上傳
   *
   * @return bool
   */
  public function canReupload(): bool
  {
    return $this->status === self::STATUS_SUBMITTED;
  }

  /**
   * 發送婉拒通知信件給申請人
   *
   * @param string|null $reason 婉拒原因
   * @return void
   */
  private function sendDeclineNotification(?string $reason = null): void
  {
    try {
      $applicant = $this->applicant;
      $recommender = $this->recommender;

      if (!$applicant || !$recommender) {
        Log::warning('無法發送婉拒通知信件：申請人或推薦人資料不完整', [
          'recommendation_id' => $this->id,
          'applicant_id' => $this->applicant_id,
          'recommender_email' => $this->recommender_email
        ]);
        return;
      }

      $emailService = new \App\Services\EmailService();
      $emailService->sendDeclineNotificationEmail($this, $reason);

      Log::info('推薦函婉拒通知信件已發送', [
        'recommendation_id' => $this->id,
        'applicant_email' => $applicant->user->email,
        'recommender_name' => $recommender->name ?? 'Unknown',
        'decline_reason' => $reason
      ]);
    } catch (\Exception $e) {
      Log::error('發送推薦函婉拒通知信件失敗: ' . $e->getMessage(), [
        'recommendation_id' => $this->id,
        'applicant_id' => $this->applicant_id,
        'recommender_email' => $this->recommender_email
      ]);
    }
  }
}
